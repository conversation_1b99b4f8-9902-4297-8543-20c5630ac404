2025-07-04 16:36:21,647 DEBUG cd velocitec && python3 -m venv env
2025-07-04 16:36:30,266 DEBUG cd velocitec && /home/<USER>/dev/velocitec/env/bin/python -m pip install --quiet --upgrade pip
2025-07-04 16:36:39,120 DEBUG cd velocitec && /home/<USER>/dev/velocitec/env/bin/python -m pip install --quiet wheel
2025-07-04 16:36:42,597 LOG Getting frappe
2025-07-04 16:36:42,597 DEBUG cd velocitec/apps && git clone https://github.com/frappe/frappe.git --branch version-15 --depth 1 --origin upstream
2025-07-04 16:37:42,537 LOG Installing frappe
2025-07-04 16:37:42,540 DEBUG cd /home/<USER>/dev/velocitec && /home/<USER>/dev/velocitec/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/dev/velocitec/apps/frappe 
2025-07-04 16:42:14,871 DEBUG cd /home/<USER>/dev/velocitec/apps/frappe && yarn install --check-files
2025-07-04 16:42:27,712 DEBUG cd velocitec && bench build
2025-07-04 16:42:28,019 INFO /home/<USER>/dev/dev/bin/bench build
2025-07-04 16:43:10,397 LOG setting up backups
2025-07-04 16:43:10,417 LOG backups were set up
2025-07-04 16:43:10,417 INFO Bench velocitec initialized
2025-07-04 16:43:11,017 INFO A newer version of bench is available: 5.25.1 → 5.25.9
2025-07-04 16:44:01,587 INFO /home/<USER>/dev/dev/bin/bench new-site velocitech.com
2025-07-04 16:45:15,284 INFO /home/<USER>/dev/dev/bin/bench get-app --branch version-15 payments
2025-07-04 16:45:16,251 LOG Getting payments
2025-07-04 16:45:16,251 DEBUG cd ./apps && git clone https://github.com/frappe/payments.git --branch version-15 --depth 1 --origin upstream
2025-07-04 16:45:18,567 LOG Installing payments
2025-07-04 16:45:18,567 DEBUG cd /home/<USER>/dev/velocitec && /home/<USER>/dev/velocitec/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/dev/velocitec/apps/payments 
2025-07-04 16:45:29,804 DEBUG bench build --app payments
2025-07-04 16:45:30,056 INFO /home/<USER>/dev/dev/bin/bench build --app payments
2025-07-04 16:45:33,609 INFO A newer version of bench is available: 5.25.1 → 5.25.9
2025-07-04 16:45:46,898 INFO /home/<USER>/dev/dev/bin/bench get-app --branch version-15 erpnext
2025-07-04 16:45:47,782 LOG Getting erpnext
2025-07-04 16:45:47,782 DEBUG cd ./apps && git clone https://github.com/frappe/erpnext.git --branch version-15 --depth 1 --origin upstream
2025-07-04 16:48:51,109 LOG Installing erpnext
2025-07-04 16:48:51,109 DEBUG cd /home/<USER>/dev/velocitec && /home/<USER>/dev/velocitec/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/dev/velocitec/apps/erpnext 
2025-07-04 16:49:38,251 DEBUG cd /home/<USER>/dev/velocitec/apps/erpnext && yarn install --check-files
2025-07-04 16:49:39,116 DEBUG bench build --app erpnext
2025-07-04 16:49:39,393 INFO /home/<USER>/dev/dev/bin/bench build --app erpnext
2025-07-04 16:49:46,748 INFO A newer version of bench is available: 5.25.1 → 5.25.9
2025-07-04 16:50:24,593 INFO /home/<USER>/dev/dev/bin/bench get-app https://github.com/Velocetec-Ltd/velocetec-erpnext.git
2025-07-04 16:50:24,609 LOG Getting velocetec-erpnext
2025-07-04 16:50:24,609 DEBUG cd ./apps && git clone https://github.com/Velocetec-Ltd/velocetec-erpnext.git  --depth 1 --origin upstream
2025-07-04 16:51:59,315 WARNING cd ./apps && git clone https://github.com/Velocetec-Ltd/velocetec-erpnext.git  --depth 1 --origin upstream executed with exit code 128
2025-07-04 16:51:59,316 WARNING /home/<USER>/dev/dev/bin/bench get-app https://github.com/Velocetec-Ltd/velocetec-erpnext.git executed with exit code 1
2025-07-04 16:51:59,833 INFO A newer version of bench is available: 5.25.1 → 5.25.9
2025-07-04 16:53:02,466 INFO /home/<USER>/dev/dev/bin/bench get-app https://github.com/Velocetec-Ltd/velocetec-erpnext.git
2025-07-04 16:53:02,483 LOG Getting velocetec-erpnext
2025-07-04 16:53:02,483 DEBUG cd ./apps && git clone https://github.com/Velocetec-Ltd/velocetec-erpnext.git  --depth 1 --origin upstream
2025-07-04 16:53:48,124 LOG Installing velocetec
2025-07-04 16:53:48,125 DEBUG cd /home/<USER>/dev/velocitec && /home/<USER>/dev/velocitec/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/dev/velocitec/apps/velocetec 
2025-07-04 16:53:52,327 DEBUG bench build --app velocetec
2025-07-04 16:53:52,654 INFO /home/<USER>/dev/dev/bin/bench build --app velocetec
2025-07-04 16:53:58,566 INFO A newer version of bench is available: 5.25.1 → 5.25.9
2025-07-04 17:31:00,094 INFO /home/<USER>/dev/dev/bin/bench --site velocitech.com payments
2025-07-04 17:31:14,075 INFO /home/<USER>/dev/dev/bin/bench --site velocitech.com install-app payments
2025-07-04 17:31:26,899 INFO /home/<USER>/dev/dev/bin/bench --site velocitech.com install-app erpnext
2025-07-04 17:33:36,211 INFO /home/<USER>/dev/dev/bin/bench --site velocitech.com install-app velocetec
2025-07-04 17:33:55,359 INFO /home/<USER>/dev/dev/bin/bench --site velocitech.com install-app velocetec
2025-07-04 18:00:01,471 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-06 12:00:01,164 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-06 18:00:01,735 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-07 18:00:02,065 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-08 12:00:01,297 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-08 14:45:25,451 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-08 14:45:25,616 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-08 14:45:25,626 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-08 14:45:25,641 INFO /home/<USER>/dev/dev/bin/bench serve --port 8002
2025-07-08 14:45:25,641 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-08 14:47:22,574 INFO /home/<USER>/dev/dev/bin/bench --site velocitech.com add-to-hosts
2025-07-08 14:47:32,305 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-08 14:47:32,470 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-08 14:47:32,496 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-08 14:47:32,504 INFO /home/<USER>/dev/dev/bin/bench serve --port 8002
2025-07-08 14:47:32,513 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-08 15:51:43,152 INFO /home/<USER>/dev/dev/bin/bench get-app https://github.com/The-Commit-Company/raven.git
2025-07-08 15:51:43,165 LOG Getting raven
2025-07-08 15:51:43,166 DEBUG cd ./apps && git clone https://github.com/The-Commit-Company/raven.git  --depth 1 --origin upstream
2025-07-08 15:51:49,944 LOG Installing raven
2025-07-08 15:51:49,944 DEBUG cd /home/<USER>/dev/velocitec && /home/<USER>/dev/velocitec/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/dev/velocitec/apps/raven 
2025-07-08 15:53:11,360 DEBUG cd /home/<USER>/dev/velocitec/apps/raven && yarn install --check-files
2025-07-08 15:57:31,713 DEBUG bench build --app raven
2025-07-08 15:57:31,826 INFO /home/<USER>/dev/dev/bin/bench build --app raven
2025-07-08 15:57:48,506 INFO A newer version of bench is available: 5.25.1 → 5.25.9
2025-07-08 16:01:28,690 INFO /home/<USER>/dev/dev/bin/bench --site velocitech.com install-app raven
2025-07-08 16:02:26,980 INFO /home/<USER>/dev/dev/bin/bench --site velocitech.com restore /home/<USER>/Downloads/20250708_134114-velocetec-dev_frappe_cloud-database.sql.gz
2025-07-08 16:03:51,393 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-08 16:03:51,563 INFO /home/<USER>/dev/dev/bin/bench serve --port 8002
2025-07-08 16:03:51,574 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-08 16:03:51,578 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-08 16:03:51,594 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-08 16:05:18,391 INFO /home/<USER>/dev/dev/bin/bench --site velocitech.com remove-from-installed-apps print_designer
2025-07-08 16:05:32,670 INFO /home/<USER>/dev/dev/bin/bench --site velocitech.com remove-from-installed-apps crm
2025-07-08 16:05:49,099 INFO /home/<USER>/dev/dev/bin/bench --site velocitech.com remove-from-installed-apps gameplan
2025-07-08 16:05:59,688 INFO /home/<USER>/dev/dev/bin/bench --site velocitech.com remove-from-installed-apps insights
2025-07-08 16:06:09,721 INFO /home/<USER>/dev/dev/bin/bench --site velocitech.com remove-from-installed-apps hrms
2025-07-08 16:06:42,912 INFO /home/<USER>/dev/dev/bin/bench --site velocitech.com set-admin-password root
2025-07-08 16:07:00,564 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-08 16:07:00,763 INFO /home/<USER>/dev/dev/bin/bench serve --port 8002
2025-07-08 16:07:00,763 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-08 16:07:00,763 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-08 16:07:00,763 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-08 16:31:38,144 INFO /home/<USER>/dev/dev/bin/bench --site velocitech.com clear-cache
2025-07-08 18:00:01,289 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-09 00:08:15,055 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-09 06:00:01,473 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-09 08:46:07,675 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-09 08:46:08,763 INFO /home/<USER>/dev/dev/bin/bench serve --port 8002
2025-07-09 08:46:08,779 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-09 08:46:08,853 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-09 08:46:09,056 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-09 09:48:23,457 INFO /home/<USER>/dev/dev/bin/bench --site velocitech.com migrate
2025-07-09 09:50:42,915 INFO /home/<USER>/dev/dev/bin/bench --site velocitech.com migrate
2025-07-09 11:59:37,122 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-09 11:59:37,724 INFO /home/<USER>/dev/dev/bin/bench serve --port 8002
2025-07-09 11:59:37,733 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-09 11:59:37,753 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-09 11:59:37,769 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-09 12:00:01,758 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-09 18:00:01,740 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-10 11:45:25,896 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-10 11:45:26,395 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-10 11:45:26,405 INFO /home/<USER>/dev/dev/bin/bench serve --port 8002
2025-07-10 11:45:26,432 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-10 11:45:26,472 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-10 12:00:01,472 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-10 12:57:55,851 INFO /home/<USER>/dev/dev/bin/bench --site mysite migrate
2025-07-10 12:58:28,927 INFO /home/<USER>/dev/dev/bin/bench --site velocitec.com migrate
2025-07-10 12:58:37,172 INFO /home/<USER>/dev/dev/bin/bench --site velocitech.com migrate
2025-07-10 17:37:22,337 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-10 17:37:22,825 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-10 17:37:22,840 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-10 17:37:22,845 INFO /home/<USER>/dev/dev/bin/bench serve --port 8002
2025-07-10 17:37:22,894 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-10 18:00:01,885 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-11 09:27:20,160 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-11 09:27:20,751 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-11 09:27:20,762 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-11 09:27:20,776 INFO /home/<USER>/dev/dev/bin/bench serve --port 8002
2025-07-11 09:27:20,789 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-11 09:29:04,369 INFO /home/<USER>/dev/dev/bin/bench --site velocitech.com migrate
2025-07-11 10:03:21,589 INFO /home/<USER>/dev/dev/bin/bench --site velocitech.com migrate
2025-07-11 10:54:16,811 INFO /home/<USER>/dev/dev/bin/bench set-config -g server_script_enabled 1
2025-07-11 10:54:19,682 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-11 10:54:19,887 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-11 10:54:19,899 INFO /home/<USER>/dev/dev/bin/bench serve --port 8002
2025-07-11 10:54:19,902 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-11 10:54:19,913 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-11 12:00:01,986 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-11 12:48:16,151 INFO /home/<USER>/dev/dev/bin/bench use velocitech.com
2025-07-11 12:48:21,271 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-11 12:48:21,466 INFO /home/<USER>/dev/dev/bin/bench serve --port 8002
2025-07-11 12:48:21,477 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-11 12:48:21,485 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-11 12:48:21,507 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-11 12:48:34,605 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-11 12:48:34,794 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-11 12:48:34,808 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-11 12:48:34,813 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-11 12:48:34,827 INFO /home/<USER>/dev/dev/bin/bench serve --port 8002
2025-07-11 13:35:59,730 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-11 13:35:59,929 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-11 13:35:59,931 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-11 13:35:59,944 INFO /home/<USER>/dev/dev/bin/bench serve --port 8002
2025-07-11 13:35:59,948 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-11 13:36:53,958 INFO /home/<USER>/dev/dev/bin/bench owrker
2025-07-11 13:37:00,646 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-11 13:37:09,609 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-11 13:37:09,818 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-11 13:37:09,819 INFO /home/<USER>/dev/dev/bin/bench serve --port 8002
2025-07-11 13:37:09,828 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-11 13:37:09,837 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-11 13:37:20,609 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-11 13:38:48,595 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-11 13:38:48,786 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-11 13:38:48,790 INFO /home/<USER>/dev/dev/bin/bench serve --port 8000
2025-07-11 13:38:48,809 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-11 13:38:48,822 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-11 13:39:11,488 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-11 13:39:11,686 INFO /home/<USER>/dev/dev/bin/bench serve --port 8002
2025-07-11 13:39:11,687 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-11 13:39:11,701 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-11 13:39:11,702 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-11 16:12:25,559 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-11 16:12:26,212 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-11 16:12:26,242 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-11 16:12:26,262 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-11 16:12:26,277 INFO /home/<USER>/dev/dev/bin/bench serve --port 8002
2025-07-11 18:00:01,736 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-13 06:59:32,082 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-13 06:59:32,827 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-13 06:59:33,102 INFO /home/<USER>/dev/dev/bin/bench serve --port 8002
2025-07-13 06:59:33,147 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-13 06:59:33,164 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-13 12:00:01,407 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-13 18:00:02,231 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-14 12:00:01,511 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-14 18:00:01,423 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-15 00:00:01,410 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-15 12:00:01,420 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-15 18:00:02,109 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-16 00:00:02,090 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-16 06:00:01,358 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-16 09:26:15,927 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-16 09:26:16,688 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-16 09:26:16,719 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-16 09:26:16,744 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-16 09:26:16,840 INFO /home/<USER>/dev/dev/bin/bench serve --port 8002
2025-07-16 09:27:29,141 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-16 09:27:29,859 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-16 09:27:29,920 INFO /home/<USER>/dev/dev/bin/bench serve --port 8002
2025-07-16 09:27:30,030 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-16 09:27:30,073 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-16 12:00:02,285 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-16 15:23:07,482 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-16 15:23:08,077 INFO /home/<USER>/dev/dev/bin/bench serve --port 8002
2025-07-16 15:23:08,079 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-16 15:23:08,091 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-16 15:23:08,092 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-16 15:23:58,926 INFO /home/<USER>/dev/dev/bin/bench --site velicitech.com migrate
2025-07-16 15:24:16,839 INFO /home/<USER>/dev/dev/bin/bench --site velocitech.com migrate
2025-07-16 15:57:22,064 INFO /home/<USER>/dev/dev/bin/bench --site velocitech.com migrate
2025-07-16 18:00:01,897 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-17 12:00:01,422 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-17 14:56:08,935 INFO /home/<USER>/dev/dev/bin/bench --site velocitech.com mariadb
2025-07-17 17:36:48,812 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-17 17:36:49,444 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-17 17:36:49,450 INFO /home/<USER>/dev/dev/bin/bench serve --port 8002
2025-07-17 17:36:49,477 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-17 17:36:49,492 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-17 18:00:01,950 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-18 00:00:01,620 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-18 12:00:01,500 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-18 18:00:02,026 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-20 00:00:01,876 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-20 18:00:01,763 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-21 00:00:01,916 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-21 06:00:01,560 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-21 12:00:01,368 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-21 18:00:01,517 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-22 12:00:01,570 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-22 18:00:01,981 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-23 00:00:01,676 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-23 12:00:02,035 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-23 18:00:01,494 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-24 11:24:42,805 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-24 11:24:43,304 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-24 11:24:43,307 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-24 11:24:43,362 INFO /home/<USER>/dev/dev/bin/bench serve --port 8002
2025-07-24 11:24:43,372 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-24 12:00:01,443 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-24 14:05:52,815 INFO /home/<USER>/dev/dev/bin/bench --site velocitch.com migrate
2025-07-24 14:06:15,824 INFO /home/<USER>/dev/dev/bin/bench --site velocitech.com migrate
2025-07-24 14:07:08,394 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-24 14:07:08,936 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-24 14:07:08,969 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-24 14:07:08,993 INFO /home/<USER>/dev/dev/bin/bench serve --port 8002
2025-07-24 14:07:08,994 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-24 18:00:01,504 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-25 12:00:02,206 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-25 18:00:02,325 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-27 18:00:01,854 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-28 12:00:01,354 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-28 17:48:10,626 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-28 17:48:11,332 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-28 17:48:11,342 INFO /home/<USER>/dev/dev/bin/bench serve --port 8002
2025-07-28 17:48:11,400 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-28 17:48:11,409 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-28 18:00:02,087 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-29 12:00:01,837 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-29 18:00:01,383 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-30 00:00:02,295 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-30 12:00:01,662 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-30 18:00:02,219 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-31 00:00:01,987 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-31 10:33:52,547 INFO /home/<USER>/dev/dev/bin/bench start
2025-07-31 10:33:53,191 INFO /home/<USER>/dev/dev/bin/bench watch
2025-07-31 10:33:53,232 INFO /home/<USER>/dev/dev/bin/bench serve --port 8002
2025-07-31 10:33:53,260 INFO /home/<USER>/dev/dev/bin/bench worker
2025-07-31 10:33:53,379 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-07-31 11:56:01,473 INFO /home/<USER>/dev/dev/bin/bench --site velositech.com migrate
2025-07-31 11:56:09,862 INFO /home/<USER>/dev/dev/bin/bench --site velocitech.com migrate
2025-07-31 12:00:01,623 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-07-31 16:43:52,442 INFO /home/<USER>/dev/dev/bin/bench --site velocitech.com migrate
2025-07-31 18:00:01,535 INFO /home/<USER>/dev/dev/bin/bench --verbose --site all backup
2025-08-01 08:39:03,153 INFO /home/<USER>/dev/dev/bin/bench start
2025-08-01 08:39:03,775 INFO /home/<USER>/dev/dev/bin/bench serve --port 8002
2025-08-01 08:39:03,791 INFO /home/<USER>/dev/dev/bin/bench watch
2025-08-01 08:39:03,795 INFO /home/<USER>/dev/dev/bin/bench schedule
2025-08-01 08:39:03,803 INFO /home/<USER>/dev/dev/bin/bench worker
